{% extends "base.html" %}

{% block title %}{% if dataset %}Edit Dataset{% else %}Add New Dataset{% endif %} - Metadata Generation Framework{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-{% if dataset %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if dataset %}Edit Dataset{% else %}Add New Dataset{% endif %}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}

                        <div class="mb-3">
                            {{ form.title.label(class="form-label") }}
                            {% if form.title.errors %}
                                {{ form.title(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.title.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.title(class="form-control", placeholder="Enter dataset title") }}
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {% if form.description.errors %}
                                {{ form.description(class="form-control is-invalid", rows=4) }}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.description(class="form-control", rows=4, placeholder="Provide a detailed description of the dataset") }}
                            {% endif %}
                            <div class="form-text">A comprehensive description will improve searchability and metadata quality.</div>
                        </div>

                        <!-- File Upload Section -->
                        <div class="mb-3">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-upload me-2"></i>Dataset Source</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        {{ form.dataset_file.label(class="form-label") }}
                                        {% if form.dataset_file.errors %}
                                            {{ form.dataset_file(class="form-control is-invalid") }}
                                            <div class="invalid-feedback">
                                                {% for error in form.dataset_file.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% else %}
                                            {{ form.dataset_file(class="form-control") }}
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Supported formats: CSV, JSON, XML, TXT, TSV, Excel (.xlsx, .xls)
                                        </div>
                                    </div>

                                    <div class="text-center my-2">
                                        <span class="badge bg-secondary">OR</span>
                                    </div>

                                    <div class="mb-0">
                                        {{ form.source_url.label(class="form-label") }}
                                        {% if form.source_url.errors %}
                                            {{ form.source_url(class="form-control is-invalid") }}
                                            <div class="invalid-feedback">
                                                {% for error in form.source_url.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% else %}
                                            {{ form.source_url(class="form-control", placeholder="https://example.com/dataset.csv") }}
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Provide a direct URL to download the dataset file
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.source.label(class="form-label") }}
                                {% if form.source.errors %}
                                    {{ form.source(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.source.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.source(class="form-control", placeholder="e.g., 'World Health Organization'") }}
                                {% endif %}
                            </div>

                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.data_type.label(class="form-label") }}
                                {% if form.data_type.errors %}
                                    {{ form.data_type(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.data_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.data_type(class="form-select") }}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.category.label(class="form-label") }}
                                {% if form.category.errors %}
                                    {{ form.category(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.category.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.category(class="form-select") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-4">
                            {{ form.tags.label(class="form-label") }}
                            {% if form.tags.errors %}
                                {{ form.tags(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.tags.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.tags(class="form-control", placeholder="e.g., covid-19, healthcare, statistics") }}
                            {% endif %}
                            <div class="form-text">Separate tags with commas. Good tags improve searchability.</div>
                        </div>

                        <!-- Additional Metadata Fields -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.license.label(class="form-label") }}
                                {% if form.license.errors %}
                                    {{ form.license(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.license.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.license(class="form-control", placeholder="e.g., MIT, CC BY 4.0, Public Domain") }}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.author.label(class="form-label") }}
                                {% if form.author.errors %}
                                    {{ form.author(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.author.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.author(class="form-control", placeholder="Dataset author or organization") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-magic me-2"></i>
                            <strong>Smart Processing:</strong> Fields marked as "auto-generated" or "auto-detected" will be automatically filled using AI and NLP techniques if left empty. The system will analyze your dataset to extract metadata, generate descriptions, detect categories, and ensure FAIR compliance.
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('datasets.list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to Datasets
                            </a>
                            {{ form.submit(class="btn btn-primary", id="submitButton") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[enctype="multipart/form-data"]');
    const submitButton = document.getElementById('submitButton');
    const fileInput = document.querySelector('input[type="file"]');
    const titleInput = document.querySelector('input[name="title"]');

    if (form && submitButton) {
        form.addEventListener('submit', function(e) {
            // Get form data for loading message
            const title = titleInput ? titleInput.value.trim() : '';
            const fileName = fileInput && fileInput.files.length > 0 ? fileInput.files[0].name : '';

            // Determine loading type and message
            let loadingType = 'upload';
            let customMessage = '';

            if (fileName) {
                customMessage = `Uploading "${fileName}"...`;
            } else if (title) {
                customMessage = `Processing "${title}"...`;
                loadingType = 'processing';
            }

            // Show loading animation
            const loaderId = window.smartLoader.show(loadingType, {
                subtitle: customMessage || 'Uploading and processing dataset...',
                tips: 'This may take a few moments depending on file size and complexity'
            });

            // Update button state
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
            submitButton.disabled = true;

            // Store loader ID for cleanup
            window.currentUploadLoader = loaderId;

            // Add timeout fallback
            setTimeout(() => {
                if (window.currentUploadLoader) {
                    window.smartLoader.hide(window.currentUploadLoader);
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                }
            }, 30000); // 30 second timeout for uploads
        });
    }

    // File input change handler for preview
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Show file info
                const fileInfo = document.createElement('div');
                fileInfo.className = 'alert alert-info mt-2';
                fileInfo.innerHTML = `
                    <i class="fas fa-file me-2"></i>
                    <strong>Selected:</strong> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                `;

                // Remove existing file info
                const existingInfo = this.parentNode.querySelector('.alert-info');
                if (existingInfo) {
                    existingInfo.remove();
                }

                // Add new file info
                this.parentNode.appendChild(fileInfo);

                // Auto-fill title if empty
                if (titleInput && !titleInput.value.trim()) {
                    const baseName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
                    const cleanName = baseName.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    titleInput.value = cleanName;
                }
            }
        });
    }

    // Form validation enhancement
    if (form) {
        form.addEventListener('submit', function(e) {
            const hasFile = fileInput && fileInput.files.length > 0;
            const hasUrl = document.querySelector('input[name="source_url"]')?.value.trim();

            if (!hasFile && !hasUrl) {
                e.preventDefault();
                alert('Please either upload a file or provide a source URL.');
                return false;
            }

            if (hasFile && hasUrl) {
                e.preventDefault();
                alert('Please provide either a file upload OR a source URL, not both.');
                return false;
            }
        });
    }
});
</script>
{% endblock %}