{% extends "base.html" %}

{% block title %}Search Datasets - Metadata Generation Framework{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Search Datasets</h1>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('main.search') }}" id="searchForm">
                <div class="row g-3">
                    <div class="col-md-6">
                        {{ form.query.label(class="form-label") }}
                        {{ form.query(class="form-control", placeholder="Search by keywords, title, or description...") }}
                    </div>
                    <div class="col-md-3">
                        {{ form.category.label(class="form-label") }}
                        {{ form.category(class="form-select") }}
                    </div>
                    <div class="col-md-3">
                        {{ form.data_type.label(class="form-label") }}
                        {{ form.data_type(class="form-select") }}
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <button type="submit" class="btn btn-primary" id="searchButton">
                        <i class="fas fa-search me-1"></i> Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                {% if request.args.get('query') or request.args.get('category') or request.args.get('data_type') %}
                    Search Results
                {% else %}
                    All Datasets
                {% endif %}
                {% if total_results %}
                <small class="text-muted">({{ total_results }} total, showing {{ results|length }})</small>
                {% endif %}
            </h5>

            {% if results %}
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-sort me-1"></i> Sort by
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                    <li>
                        <a class="dropdown-item" href="{{ url_for('main.search', query=request.args.get('query'), category=request.args.get('category'), data_type=request.args.get('data_type'), sort='relevance') }}">
                            Relevance
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('main.search', query=request.args.get('query'), category=request.args.get('category'), data_type=request.args.get('data_type'), sort='date_desc') }}">
                            Newest first
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('main.search', query=request.args.get('query'), category=request.args.get('category'), data_type=request.args.get('data_type'), sort='date_asc') }}">
                            Oldest first
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ url_for('main.search', query=request.args.get('query'), category=request.args.get('category'), data_type=request.args.get('data_type'), sort='quality_desc') }}">
                            Highest quality
                        </a>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>

        <div class="card-body">
            {% if results %}
                <div class="list-group">
                    {% for result in results %}
                    {% set dataset = result.dataset if result.dataset is defined else result %}
                    <div class="search-result mb-3">
                        <div class="d-flex align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-1">
                                    <h5 class="mb-0">
                                        <a href="{{ url_for('datasets.view', dataset_id=dataset.id) }}" class="text-decoration-none">
                                            {{ dataset.title }}
                                        </a>
                                    </h5>
                                    {% if result.quality_score is defined %}
                                    <div class="ms-2">
                                        <span class="badge {{ 'bg-success' if result.quality_score >= 80 else ('bg-warning' if result.quality_score >= 60 else 'bg-secondary') }}">
                                            Quality: {{ result.quality_score|int }}%
                                        </span>
                                    </div>
                                    {% endif %}
                                </div>

                                {% if result.score is defined and result.score > 0 %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-chart-line me-1"></i>
                                        Relevance: {{ (result.score * 100)|int }}%
                                        {% if result.relevance_explanation %}
                                        | {{ result.relevance_explanation }}
                                        {% endif %}
                                    </small>
                                </div>
                                {% endif %}

                                <p class="mb-2">{{ dataset.description[:200] if dataset.description else 'No description available' }}{% if dataset.description and dataset.description|length > 200 %}...{% endif %}</p>
                                <div class="d-flex flex-wrap align-items-center mb-2">
                                    {% if dataset.category %}
                                    <span class="badge bg-secondary me-2 mb-1">{{ dataset.category }}</span>
                                    {% endif %}

                                    {% if dataset.data_type %}
                                    <span class="badge bg-info me-2 mb-1">{{ dataset.data_type }}</span>
                                    {% endif %}

                                    {% if dataset.format %}
                                    <span class="badge bg-light text-dark me-2 mb-1">{{ dataset.format }}</span>
                                    {% endif %}

                                    <span class="badge bg-light text-dark me-2 mb-1">
                                        <i class="fas fa-calendar-alt me-1"></i> {{ dataset.created_at.strftime('%b %d, %Y') }}
                                    </span>

                                    {% if dataset.source %}
                                    <span class="badge bg-light text-dark me-2 mb-1">
                                        <i class="fas fa-database me-1"></i> {{ dataset.source }}
                                    </span>
                                    {% endif %}

                                    {% if dataset.user %}
                                    <span class="badge bg-light text-dark mb-1">
                                        <i class="fas fa-user me-1"></i> {{ dataset.user.username }}
                                    </span>
                                    {% endif %}
                                </div>

                                {% if dataset.tags %}
                                <div>
                                    {% set tags = dataset.tags.split(',') if dataset.tags else [] %}
                                    {% for tag in tags %}
                                    <a href="{{ url_for('main.search', query=tag.strip()) }}" class="badge bg-primary me-1 mb-1 text-decoration-none">{{ tag.strip() }}</a>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            <div class="ms-3">
                                <a href="{{ url_for('datasets.view', dataset_id=dataset.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if pagination and pagination.total_pages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', page=pagination.prev_num, query=request.args.get('query', ''), category=request.args.get('category', ''), data_type=request.args.get('data_type', '')) }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% set start_page = [1, pagination.page - 2]|max %}
                        {% set end_page = [pagination.total_pages, pagination.page + 2]|min %}

                        {% if start_page > 1 %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', page=1, query=request.args.get('query', ''), category=request.args.get('category', ''), data_type=request.args.get('data_type', '')) }}">1</a>
                        </li>
                        {% if start_page > 2 %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                        {% endif %}
                        {% endif %}

                        {% for page_num in range(start_page, end_page + 1) %}
                            {% if page_num == pagination.page %}
                            <li class="page-item active">
                                <a class="page-link" href="#">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.search', page=page_num, query=request.args.get('query', ''), category=request.args.get('category', ''), data_type=request.args.get('data_type', '')) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if end_page < pagination.total_pages %}
                        {% if end_page < pagination.total_pages - 1 %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                        {% endif %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', page=pagination.total_pages, query=request.args.get('query', ''), category=request.args.get('category', ''), data_type=request.args.get('data_type', '')) }}">{{ pagination.total_pages }}</a>
                        </li>
                        {% endif %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.search', page=pagination.next_num, query=request.args.get('query', ''), category=request.args.get('category', ''), data_type=request.args.get('data_type', '')) }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-database fa-4x text-muted"></i>
                    </div>
                    {% if request.args.get('query') or request.args.get('category') or request.args.get('data_type') %}
                    <h4>No results found</h4>
                    <p class="text-muted">Try adjusting your search terms or filters</p>
                    {% else %}
                    <h4>No datasets available</h4>
                    <p class="text-muted">No datasets have been uploaded yet</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    const searchButton = document.getElementById('searchButton');

    if (searchForm && searchButton) {
        searchForm.addEventListener('submit', function(e) {
            const queryInput = document.querySelector('input[name="query"]');
            const query = queryInput ? queryInput.value.trim() : '';

            if (query) {
                // Show enhanced search loading with query details
                const loaderId = window.smartLoader.showSearchLoader(query);

                // Update button state
                searchButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Searching...';
                searchButton.disabled = true;

                // Store loader ID for cleanup
                window.currentSearchLoader = loaderId;

                // Add timeout fallback in case page doesn't redirect
                setTimeout(() => {
                    if (window.currentSearchLoader) {
                        window.smartLoader.hide(window.currentSearchLoader);
                        searchButton.innerHTML = '<i class="fas fa-search me-1"></i> Search';
                        searchButton.disabled = false;
                    }
                }, 10000); // 10 second timeout
            }
        });
    }

    // Enhanced loading for pagination links
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const url = this.getAttribute('href');
            if (url && url !== '#') {
                const urlParams = new URLSearchParams(url.split('?')[1]);
                const query = urlParams.get('query');

                if (query) {
                    const loaderId = window.smartLoader.showSearchLoader(query);
                    window.currentSearchLoader = loaderId;
                }
            }
        });
    });

    // Enhanced loading for tag links
    const tagLinks = document.querySelectorAll('.badge.bg-primary[href*="search"]');
    tagLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const url = this.getAttribute('href');
            if (url) {
                const urlParams = new URLSearchParams(url.split('?')[1]);
                const query = urlParams.get('query');

                if (query) {
                    const loaderId = window.smartLoader.showSearchLoader(query);
                    window.currentSearchLoader = loaderId;
                }
            }
        });
    });

    // Real-time search suggestions (optional enhancement)
    const queryInput = document.querySelector('input[name="query"]');
    if (queryInput) {
        let searchTimeout;

        queryInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 3) {
                searchTimeout = setTimeout(() => {
                    // Could implement live search suggestions here
                    console.log('Could show search suggestions for:', query);
                }, 500);
            }
        });
    }
});
</script>
{% endblock %}