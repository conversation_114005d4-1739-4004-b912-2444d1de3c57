{% extends "base.html" %}

{% block title %}Register - Metadata Generation Framework{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Create Account</h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="{{ url_for('auth.register') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {% if form.username.errors %}
                                {{ form.username(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.username(class="form-control", placeholder="Choose a username") }}
                            {% endif %}
                            <div class="form-text">Username should be between 3-64 characters.</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {% if form.email.errors %}
                                {{ form.email(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.email(class="form-control", placeholder="Enter your email address") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {% if form.password.errors %}
                                {{ form.password(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.password(class="form-control", placeholder="Create a password") }}
                            {% endif %}
                            <div class="form-text">Password must be at least 8 characters long.</div>
                        </div>
                        
                        <div class="mb-4">
                            {{ form.password2.label(class="form-label") }}
                            {% if form.password2.errors %}
                                {{ form.password2(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.password2(class="form-control", placeholder="Confirm your password") }}
                            {% endif %}
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">Already have an account? <a href="{{ url_for('auth.login') }}" class="text-primary">Login</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}