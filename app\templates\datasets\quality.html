{% extends "base.html" %}

{% block title %}Dataset Quality Assessment - {{ dataset.title }}{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('datasets.list') }}">Datasets</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('datasets.view', dataset_id=dataset.id) }}">{{ dataset.title }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Quality Assessment</li>
                </ol>
            </nav>
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="mb-0">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        Quality Assessment
                    </h1>
                    <p class="text-muted">{{ dataset.title }}</p>
                </div>
                {% if quality %}
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" id="reportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-medical-alt me-2"></i>
                        Generate Health Report
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="reportDropdown">
                        <li><a class="dropdown-item" href="{{ url_for('reports.view_report', dataset_id=dataset.id, format='html') }}" target="_blank">
                            <i class="fas fa-file-code me-2"></i> View HTML Report
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('reports.view_report', dataset_id=dataset.id, format='pdf') }}">
                            <i class="fas fa-file-pdf me-2"></i> Download PDF Report
                        </a></li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    {% if not quality %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        This dataset has not been assessed for quality yet.
        <div class="mt-3">
            <a href="{{ url_for('datasets.assess_quality', dataset_id=dataset.id) }}" class="btn btn-primary">
                <i class="fas fa-chart-line me-2"></i>
                Assess Quality
            </a>
        </div>
    </div>
    {% else %}
    
    <!-- Overall Quality Score -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-lg-3 text-center">
                            <div class="quality-score-circle 
                                {% if quality.quality_score >= 80 %}bg-success{% elif quality.quality_score >= 60 %}bg-info{% elif quality.quality_score >= 40 %}bg-warning{% else %}bg-danger{% endif %}">
                                <h2 class="mb-0">{{ quality.quality_score|round|int }}</h2>
                                <p class="mb-0 small">out of 100</p>
                            </div>
                            <h4 class="mt-2">Overall Quality</h4>
                            <p class="text-muted small mb-0">Last assessed: {{ quality.assessment_date.strftime('%b %d, %Y') }}</p>
                        </div>
                        <div class="col-lg-9">
                            <h4>Quality Assessment Summary</h4>
                            <p>This dataset's quality has been evaluated across multiple dimensions using our advanced scoring algorithms. The overall quality score is calculated as a weighted average of the dimension scores.</p>
                            
                            <div class="progress-labels d-flex justify-content-between mb-1 mt-4">
                                <span>Poor</span>
                                <span>Fair</span>
                                <span>Good</span>
                                <span>Excellent</span>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 25%"></div>
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 25%"></div>
                                <div class="progress-bar bg-info" role="progressbar" style="width: 25%"></div>
                                <div class="progress-bar bg-success" role="progressbar" style="width: 25%"></div>
                            </div>
                            <div class="position-relative mt-1">
                                <div class="position-absolute" style="left: calc({{ quality.quality_score }}% - 8px);">
                                    <i class="fas fa-caret-up"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Dimension Scores -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Quality Dimensions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% set dimensions = [
                            {'name': 'Completeness', 'score': quality.completeness, 'icon': 'fa-check-circle', 'description': 'Presence of required and recommended metadata fields'},
                            {'name': 'Consistency', 'score': quality.consistency, 'icon': 'fa-sync', 'description': 'Consistency of data formats and values'},
                            {'name': 'Accuracy', 'score': quality.accuracy, 'icon': 'fa-bullseye', 'description': 'Accuracy and correctness of the data content'},
                            {'name': 'Timeliness', 'score': quality.timeliness, 'icon': 'fa-clock', 'description': 'Freshness and update frequency of the dataset'},
                            {'name': 'Conformity', 'score': quality.conformity, 'icon': 'fa-certificate', 'description': 'Compliance with standards like Schema.org and FAIR principles'},
                            {'name': 'Integrity', 'score': quality.integrity, 'icon': 'fa-shield-alt', 'description': 'Data integrity and validation'}
                        ] %}
                        
                        {% for dim in dimensions %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="dimension-card p-3 h-100">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="dimension-icon me-3 
                                        {% if dim.score >= 80 %}bg-success{% elif dim.score >= 60 %}bg-info{% elif dim.score >= 40 %}bg-warning{% else %}bg-danger{% endif %}">
                                        <i class="fas {{ dim.icon }}"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0">{{ dim.name }}</h5>
                                        <div class="score-badge 
                                            {% if dim.score >= 80 %}bg-success{% elif dim.score >= 60 %}bg-info{% elif dim.score >= 40 %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ dim.score|round|int }}/100
                                        </div>
                                    </div>
                                </div>
                                <p class="small mb-2">{{ dim.description }}</p>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar 
                                        {% if dim.score >= 80 %}bg-success{% elif dim.score >= 60 %}bg-info{% elif dim.score >= 40 %}bg-warning{% else %}bg-danger{% endif %}" 
                                        role="progressbar" style="width: {{ dim.score }}%"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAIR Compliance -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">FAIR Principles Compliance</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <p>The FAIR Data Principles emphasize Findability, Accessibility, Interoperability, and Reusability of data and metadata.</p>
                            
                            <div class="row mt-4">
                                {% set fair_principles = [
                                    {'name': 'Findable', 'letter': 'F', 'score': quality.findable_score, 'color': 'success', 'description': 'Data can be found by humans and machines'},
                                    {'name': 'Accessible', 'letter': 'A', 'score': quality.accessible_score, 'color': 'info', 'description': 'Data can be accessed with appropriate authentication'},
                                    {'name': 'Interoperable', 'letter': 'I', 'score': quality.interoperable_score, 'color': 'warning', 'description': 'Data can be integrated with other data and used by applications'},
                                    {'name': 'Reusable', 'letter': 'R', 'score': quality.reusable_score, 'color': 'danger', 'description': 'Data can be reused under clear licensing and provenance'}
                                ] %}
                                
                                {% for principle in fair_principles %}
                                <div class="col-md-6 mb-3">
                                    <div class="fair-principle-card">
                                        <div class="d-flex align-items-center">
                                            <div class="fair-letter bg-{{ principle.color }} me-3">{{ principle.letter }}</div>
                                            <div>
                                                <h5 class="mb-0">{{ principle.name }}</h5>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress flex-grow-1 me-2" style="height: 6px;">
                                                        <div class="progress-bar bg-{{ principle.color }}" role="progressbar" 
                                                            style="width: {{ principle.score }}%"></div>
                                                    </div>
                                                    <span class="score-value">{{ principle.score|round|int }}%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="small mt-2 mb-0">{{ principle.description }}</p>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="fair-compliance-status text-center p-4 mb-3">
                                {% set fair_status_text = (dataset.fair_score or 0) | fair_status_text %}
                                <div class="status-indicator mb-3
                                    {% if (dataset.fair_score or 0) >= 80 %}bg-success{% elif (dataset.fair_score or 0) >= 50 %}bg-warning{% else %}bg-danger{% endif %}">
                                    <i class="fas {% if (dataset.fair_score or 0) >= 80 %}fa-check{% elif (dataset.fair_score or 0) >= 50 %}fa-exclamation-triangle{% else %}fa-times{% endif %}"></i>
                                </div>
                                <h4>FAIR Compliance</h4>
                                <p class="mb-0">
                                    Status: <strong>{{ fair_status_text }}</strong> ({{ "%.1f"|format(dataset.fair_score or 0) }}%)
                                </p>
                                {% if dataset.persistent_identifier %}
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <strong>Persistent ID:</strong><br>
                                        <code>{{ dataset.persistent_identifier }}</code>
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="schema-org-status text-center p-4">
                                <div class="status-indicator mb-3 
                                    {% if quality.schema_org_compliant %}bg-success{% else %}bg-warning{% endif %}">
                                    <i class="fas {% if quality.schema_org_compliant %}fa-check{% else %}fa-exclamation-triangle{% endif %}"></i>
                                </div>
                                <h4>Schema.org Compliance</h4>
                                <p class="mb-0">
                                    {% if quality.schema_org_compliant %}
                                    This dataset has valid Schema.org metadata.
                                    {% else %}
                                    This dataset's Schema.org metadata needs improvement.
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAIR Metadata Details -->
    {% if dataset.fair_metadata or dataset.dublin_core or dataset.dcat_metadata %}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-database me-2"></i>
                        FAIR Metadata Standards
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if dataset.persistent_identifier %}
                        <div class="col-md-12 mb-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-link me-2"></i>Persistent Identifier</h6>
                                <p class="mb-0">
                                    This dataset has been assigned a persistent identifier for long-term findability:
                                    <br><strong><code>{{ dataset.persistent_identifier }}</code></strong>
                                </p>
                            </div>
                        </div>
                        {% endif %}

                        <div class="col-md-4 mb-3">
                            <div class="metadata-standard-card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tags me-2"></i>
                                        Dublin Core
                                    </h6>
                                </div>
                                <div class="card-body">
                                    {% if dataset.dublin_core %}
                                    <p class="text-success mb-2">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Available
                                    </p>
                                    <small class="text-muted">
                                        Standard metadata elements for resource description
                                    </small>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="showMetadata('dublin_core')">
                                            View Details
                                        </button>
                                    </div>
                                    {% else %}
                                    <p class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Not Available
                                    </p>
                                    <small class="text-muted">
                                        Dublin Core metadata not generated
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="metadata-standard-card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-sitemap me-2"></i>
                                        DCAT
                                    </h6>
                                </div>
                                <div class="card-body">
                                    {% if dataset.dcat_metadata %}
                                    <p class="text-success mb-2">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Available
                                    </p>
                                    <small class="text-muted">
                                        Data Catalog Vocabulary for web-based data catalogs
                                    </small>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-info" onclick="showMetadata('dcat')">
                                            View Details
                                        </button>
                                    </div>
                                    {% else %}
                                    <p class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Not Available
                                    </p>
                                    <small class="text-muted">
                                        DCAT metadata not generated
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="metadata-standard-card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-code me-2"></i>
                                        JSON-LD
                                    </h6>
                                </div>
                                <div class="card-body">
                                    {% if dataset.json_ld %}
                                    <p class="text-success mb-2">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Available
                                    </p>
                                    <small class="text-muted">
                                        Linked Data format for machine-readable metadata
                                    </small>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-success" onclick="showMetadata('json_ld')">
                                            View Details
                                        </button>
                                    </div>
                                    {% else %}
                                    <p class="text-warning mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Not Available
                                    </p>
                                    <small class="text-muted">
                                        JSON-LD metadata not generated
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if dataset.fair_metadata %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-info-circle me-2"></i>Comprehensive FAIR Metadata</h6>
                                <p class="mb-2">
                                    This dataset includes comprehensive FAIR-compliant metadata covering all four principles:
                                    Findable, Accessible, Interoperable, and Reusable.
                                </p>
                                <button class="btn btn-sm btn-outline-dark" onclick="showMetadata('fair_metadata')">
                                    <i class="fas fa-eye me-1"></i>
                                    View Full FAIR Metadata
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recommendations and Issues -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Recommendations</h5>
                </div>
                <div class="card-body">
                    {% if quality.recommendations_list %}
                    <ul class="recommendations-list">
                        {% for recommendation in quality.recommendations_list %}
                        <li>
                            <div class="recommendation-item">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                {{ recommendation }}
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-center py-4 text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                        No recommendations available
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Issues</h5>
                </div>
                <div class="card-body">
                    {% if quality.issues_list %}
                    <ul class="issues-list">
                        {% for issue in quality.issues_list %}
                        <li>
                            <div class="issue-item">
                                <i class="fas fa-exclamation-circle text-danger me-2"></i>
                                {{ issue }}
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-center py-4 text-muted">
                        <i class="fas fa-check-circle fa-2x mb-2"></i><br>
                        No issues detected
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* Quality Score Styles */
    .quality-score-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #fff;
        margin: 0 auto;
    }
    
    .dimension-card {
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .dimension-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }
    
    .dimension-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
    }
    
    .score-badge {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 4px;
        color: #fff;
        font-size: 0.75rem;
        margin-top: 4px;
    }
    
    .fair-letter {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-weight: bold;
    }
    
    .fair-principle-card {
        padding: 1rem;
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
        height: 100%;
    }
    
    .status-indicator {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 1.5rem;
        margin: 0 auto;
    }
    
    .fair-compliance-status, .schema-org-status {
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
    }
    
    .recommendations-list, .issues-list {
        list-style-type: none;
        padding-left: 0;
    }
    
    .recommendation-item, .issue-item {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border-radius: 8px;
        background-color: rgba(0,0,0,0.03);
    }

    .metadata-standard-card {
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .metadata-standard-card:hover {
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
</style>
{% endblock %}

<!-- Metadata Viewer Modal -->
<div class="modal fade" id="metadataModal" tabindex="-1" aria-labelledby="metadataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="metadataModalLabel">Metadata Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="metadataContent">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="copyMetadata()">
                    <i class="fas fa-copy me-1"></i>
                    Copy to Clipboard
                </button>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
{{ super() }}
<script>
let currentMetadata = '';

function showMetadata(type) {
    const modal = new bootstrap.Modal(document.getElementById('metadataModal'));
    const modalTitle = document.getElementById('metadataModalLabel');
    const modalContent = document.getElementById('metadataContent');

    // Set title based on type
    const titles = {
        'dublin_core': 'Dublin Core Metadata',
        'dcat': 'DCAT Metadata',
        'json_ld': 'JSON-LD Structured Data',
        'fair_metadata': 'Comprehensive FAIR Metadata'
    };

    modalTitle.textContent = titles[type] || 'Metadata Details';

    // Show loading
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    modal.show();

    // Get metadata from dataset
    const metadata = getDatasetMetadata(type);

    if (metadata) {
        try {
            const parsed = JSON.parse(metadata);
            currentMetadata = JSON.stringify(parsed, null, 2);

            modalContent.innerHTML = `
                <div class="bg-light p-3 rounded">
                    <pre><code class="language-json">${escapeHtml(currentMetadata)}</code></pre>
                </div>
            `;
        } catch (e) {
            modalContent.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error parsing metadata: ${e.message}
                </div>
            `;
        }
    } else {
        modalContent.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No ${titles[type]} available for this dataset.
            </div>
        `;
    }
}

function getDatasetMetadata(type) {
    // This would normally come from the server, but for now we'll use template data
    const metadataMap = {
        {% if dataset.dublin_core %}'dublin_core': {{ dataset.dublin_core|tojson }},{% endif %}
        {% if dataset.dcat_metadata %}'dcat': {{ dataset.dcat_metadata|tojson }},{% endif %}
        {% if dataset.json_ld %}'json_ld': {{ dataset.json_ld|tojson }},{% endif %}
        {% if dataset.fair_metadata %}'fair_metadata': {{ dataset.fair_metadata|tojson }}{% endif %}
    };

    return metadataMap[type] || null;
}

function copyMetadata() {
    if (currentMetadata) {
        navigator.clipboard.writeText(currentMetadata).then(() => {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check me-2"></i>
                        Metadata copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                document.body.removeChild(toast);
            });
        }).catch(err => {
            console.error('Failed to copy metadata:', err);
        });
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}