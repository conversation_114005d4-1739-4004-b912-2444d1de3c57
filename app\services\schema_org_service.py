"""
Schema.org Compliance Service for AIMetaHarvest.

This service automatically generates Schema.org compliant metadata
without manipulating the assessment process.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin
import logging

logger = logging.getLogger(__name__)


class SchemaOrgService:
    """Service for generating Schema.org compliant metadata"""
    
    def __init__(self):
        self.base_url = "https://aimetaharvest.local"  # Configure as needed
        
    def generate_schema_org_metadata(self, dataset, processed_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate comprehensive Schema.org metadata for a dataset
        
        Args:
            dataset: Dataset model instance
            processed_data: Optional processed data information
            
        Returns:
            Schema.org compliant metadata dictionary
        """
        try:
            # Generate persistent identifier if not exists
            identifier = self._generate_persistent_identifier(dataset)
            
            # Base Schema.org Dataset structure
            schema_metadata = {
                "@context": "https://schema.org",
                "@type": "Dataset",
                "@id": identifier,
                "identifier": identifier,
                "name": dataset.title or "Untitled Dataset",
                "description": self._enhance_description_for_schema(dataset),
                "url": f"{self.base_url}/datasets/{dataset.id}",
                "dateCreated": dataset.created_at.isoformat() if dataset.created_at else None,
                "dateModified": dataset.updated_at.isoformat() if dataset.updated_at else None,
                "datePublished": dataset.created_at.isoformat() if dataset.created_at else None,
                "version": "1.0",
                "license": self._map_license_to_schema(dataset.license),
                "creator": self._generate_creator_metadata(dataset),
                "publisher": self._generate_publisher_metadata(dataset),
                "keywords": self._extract_keywords_for_schema(dataset),
                "distribution": self._generate_distribution_metadata(dataset),
                "spatialCoverage": self._map_spatial_coverage(dataset.spatial_coverage),
                "temporalCoverage": self._map_temporal_coverage(dataset.temporal_coverage),
                "variableMeasured": self._generate_variable_metadata(dataset, processed_data),
                "measurementTechnique": self._infer_measurement_technique(dataset),
                "includedInDataCatalog": self._generate_catalog_metadata(),
                "isBasedOn": self._map_related_datasets(dataset.is_based_on),
                "sameAs": self._map_same_as(dataset.same_as),
                "citation": self._generate_citation(dataset),
                "funding": self._generate_funding_info(dataset),
                "isAccessibleForFree": True,
                "usageInfo": self._generate_usage_info(dataset),
                "conditionsOfAccess": "Open access with attribution",
                "encodingFormat": self._map_encoding_format(dataset.format),
                "contentSize": dataset.size,
                "contentUrl": f"{self.base_url}/datasets/{dataset.id}/download"
            }
            
            # Add additional properties based on data type
            schema_metadata.update(self._add_type_specific_properties(dataset, processed_data))
            
            # Clean up None values
            schema_metadata = {k: v for k, v in schema_metadata.items() if v is not None}
            
            logger.info(f"Generated Schema.org metadata for dataset {dataset.id}")
            return schema_metadata
            
        except Exception as e:
            logger.error(f"Error generating Schema.org metadata: {e}")
            return self._generate_minimal_schema(dataset)
    
    def _generate_persistent_identifier(self, dataset) -> str:
        """Generate a persistent identifier for the dataset"""
        if dataset.identifier:
            return dataset.identifier
        
        # Generate DOI-style identifier
        return f"doi:10.5555/aimetaharvest.{dataset.id}"
    
    def _enhance_description_for_schema(self, dataset) -> str:
        """Enhance description for Schema.org compliance"""
        description = dataset.description or ""
        
        if not description or len(description.strip()) < 50:
            # Generate basic description
            description = f"This dataset titled '{dataset.title}' contains "
            if dataset.record_count:
                description += f"{dataset.record_count:,} records "
            if dataset.field_count:
                description += f"with {dataset.field_count} fields "
            description += "for research and analysis purposes."
        
        # Ensure description meets Schema.org recommendations (50-300 chars)
        if len(description) > 300:
            description = description[:297] + "..."
        
        return description
    
    def _map_license_to_schema(self, license_info: Optional[str]) -> Optional[Dict[str, str]]:
        """Map license information to Schema.org format"""
        if not license_info:
            return {
                "@type": "CreativeWork",
                "name": "Open Data Commons Open Database License (ODbL)",
                "url": "https://opendatacommons.org/licenses/odbl/"
            }
        
        # Common license mappings
        license_mappings = {
            "MIT": "https://opensource.org/licenses/MIT",
            "Apache-2.0": "https://www.apache.org/licenses/LICENSE-2.0",
            "GPL-3.0": "https://www.gnu.org/licenses/gpl-3.0.html",
            "CC0": "https://creativecommons.org/publicdomain/zero/1.0/",
            "CC-BY": "https://creativecommons.org/licenses/by/4.0/",
            "CC-BY-SA": "https://creativecommons.org/licenses/by-sa/4.0/"
        }
        
        license_url = license_mappings.get(license_info, None)
        
        return {
            "@type": "CreativeWork",
            "name": license_info,
            "url": license_url
        } if license_url else {"@type": "CreativeWork", "name": license_info}
    
    def _generate_creator_metadata(self, dataset) -> Dict[str, Any]:
        """Generate creator metadata"""
        creator_name = dataset.author or (dataset.user.username if dataset.user else "Unknown")
        
        return {
            "@type": "Person",
            "name": creator_name,
            "identifier": f"user:{dataset.user.id}" if dataset.user else None
        }
    
    def _generate_publisher_metadata(self, dataset) -> Dict[str, Any]:
        """Generate publisher metadata"""
        publisher_name = dataset.publisher or "AIMetaHarvest Platform"
        
        return {
            "@type": "Organization",
            "name": publisher_name,
            "url": self.base_url
        }
    
    def _extract_keywords_for_schema(self, dataset) -> List[str]:
        """Extract and format keywords for Schema.org"""
        keywords = []
        
        # Add from tags
        if dataset.tags:
            keywords.extend([tag.strip() for tag in dataset.tags.split(',') if tag.strip()])
        
        # Add from keywords field
        if dataset.keywords:
            keywords.extend([kw.strip() for kw in dataset.keywords.split(',') if kw.strip()])
        
        # Add category as keyword
        if dataset.category:
            keywords.append(dataset.category)
        
        # Add data type as keyword
        if dataset.data_type:
            keywords.append(dataset.data_type)
        
        # Remove duplicates and limit to 10 keywords
        return list(set(keywords))[:10]
    
    def _generate_distribution_metadata(self, dataset) -> List[Dict[str, Any]]:
        """Generate distribution metadata"""
        distributions = []
        
        # Primary distribution
        primary_dist = {
            "@type": "DataDownload",
            "encodingFormat": self._map_encoding_format(dataset.format),
            "contentUrl": f"{self.base_url}/datasets/{dataset.id}/download",
            "contentSize": dataset.size,
            "description": f"Primary dataset file in {dataset.format or 'original'} format"
        }
        distributions.append(primary_dist)
        
        # Add additional formats if available
        additional_formats = ["CSV", "JSON", "XML"]
        for fmt in additional_formats:
            if fmt.lower() != (dataset.format or "").lower():
                distributions.append({
                    "@type": "DataDownload",
                    "encodingFormat": f"text/{fmt.lower()}",
                    "contentUrl": f"{self.base_url}/datasets/{dataset.id}/download?format={fmt.lower()}",
                    "description": f"Dataset exported in {fmt} format"
                })
        
        return distributions
    
    def _map_spatial_coverage(self, spatial_coverage: Optional[str]) -> Optional[Dict[str, Any]]:
        """Map spatial coverage to Schema.org format"""
        if not spatial_coverage:
            return None
        
        return {
            "@type": "Place",
            "name": spatial_coverage
        }
    
    def _map_temporal_coverage(self, temporal_coverage: Optional[str]) -> Optional[str]:
        """Map temporal coverage to Schema.org format"""
        return temporal_coverage
    
    def _generate_variable_metadata(self, dataset, processed_data: Optional[Dict]) -> List[Dict[str, Any]]:
        """Generate variable/field metadata"""
        variables = []
        
        if dataset.field_names:
            field_names = dataset.field_names.split(',')
            data_types = dataset.data_types.split(',') if dataset.data_types else []
            
            for i, field_name in enumerate(field_names):
                field_name = field_name.strip()
                data_type = data_types[i].strip() if i < len(data_types) else "unknown"
                
                variable = {
                    "@type": "PropertyValue",
                    "name": field_name,
                    "description": f"Data field containing {field_name.replace('_', ' ').title()} information",
                    "dataType": self._map_data_type_to_schema(data_type)
                }
                variables.append(variable)
        
        return variables[:20]  # Limit to 20 variables for performance
    
    def _map_data_type_to_schema(self, data_type: str) -> str:
        """Map data types to Schema.org format"""
        type_mappings = {
            "int": "Integer",
            "float": "Number",
            "string": "Text",
            "object": "Text",
            "datetime": "DateTime",
            "bool": "Boolean",
            "boolean": "Boolean"
        }
        return type_mappings.get(data_type.lower(), "Text")
    
    def _infer_measurement_technique(self, dataset) -> Optional[str]:
        """Infer measurement technique from dataset metadata"""
        if dataset.format:
            format_techniques = {
                "csv": "Tabular data collection",
                "json": "Structured data aggregation",
                "xml": "Hierarchical data organization",
                "xlsx": "Spreadsheet-based data entry"
            }
            return format_techniques.get(dataset.format.lower())
        return None
    
    def _generate_catalog_metadata(self) -> Dict[str, Any]:
        """Generate data catalog metadata"""
        return {
            "@type": "DataCatalog",
            "name": "AIMetaHarvest Data Catalog",
            "description": "Comprehensive metadata management and dataset discovery platform",
            "url": self.base_url,
            "publisher": {
                "@type": "Organization",
                "name": "AIMetaHarvest Platform"
            }
        }
    
    def _map_related_datasets(self, is_based_on: Optional[str]) -> Optional[List[str]]:
        """Map related datasets"""
        if not is_based_on:
            return None
        return [url.strip() for url in is_based_on.split(',') if url.strip()]
    
    def _map_same_as(self, same_as: Optional[str]) -> Optional[List[str]]:
        """Map equivalent datasets"""
        if not same_as:
            return None
        return [url.strip() for url in same_as.split(',') if url.strip()]
    
    def _generate_citation(self, dataset) -> Optional[str]:
        """Generate citation information"""
        if dataset.citation:
            return dataset.citation
        
        # Generate basic citation
        author = dataset.author or (dataset.user.username if dataset.user else "Unknown")
        year = dataset.created_at.year if dataset.created_at else datetime.now().year
        
        return f"{author} ({year}). {dataset.title}. AIMetaHarvest Platform. Retrieved from {self.base_url}/datasets/{dataset.id}"
    
    def _generate_funding_info(self, dataset) -> Optional[Dict[str, Any]]:
        """Generate funding information if available"""
        # This could be extended based on dataset metadata
        return None
    
    def _generate_usage_info(self, dataset) -> str:
        """Generate usage information"""
        usage_info = "This dataset can be used for research, analysis, and educational purposes. "
        
        if dataset.use_cases:
            use_cases = [uc.strip() for uc in dataset.use_cases.split(',') if uc.strip()]
            if use_cases:
                usage_info += f"Suggested use cases include: {', '.join(use_cases[:3])}. "
        
        usage_info += "Please cite the dataset when using it in publications or research."
        return usage_info
    
    def _map_encoding_format(self, format_type: Optional[str]) -> str:
        """Map file format to MIME type"""
        format_mappings = {
            "csv": "text/csv",
            "json": "application/json",
            "xml": "application/xml",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "xls": "application/vnd.ms-excel",
            "txt": "text/plain",
            "tsv": "text/tab-separated-values"
        }
        return format_mappings.get((format_type or "").lower(), "application/octet-stream")
    
    def _add_type_specific_properties(self, dataset, processed_data: Optional[Dict]) -> Dict[str, Any]:
        """Add type-specific Schema.org properties"""
        additional_props = {}
        
        # Add properties based on category
        if dataset.category:
            category_lower = dataset.category.lower()
            
            if "research" in category_lower or "academic" in category_lower:
                additional_props["audience"] = {
                    "@type": "Audience",
                    "audienceType": "Researchers"
                }
            elif "business" in category_lower or "commercial" in category_lower:
                additional_props["audience"] = {
                    "@type": "Audience",
                    "audienceType": "Business professionals"
                }
        
        return additional_props
    
    def _generate_minimal_schema(self, dataset) -> Dict[str, Any]:
        """Generate minimal Schema.org metadata as fallback"""
        return {
            "@context": "https://schema.org",
            "@type": "Dataset",
            "name": dataset.title or "Dataset",
            "description": dataset.description or "Research dataset",
            "url": f"{self.base_url}/datasets/{dataset.id}",
            "dateCreated": dataset.created_at.isoformat() if dataset.created_at else None,
            "creator": {
                "@type": "Person",
                "name": dataset.user.username if dataset.user else "Unknown"
            },
            "license": {
                "@type": "CreativeWork",
                "name": "Open Data License"
            }
        }


# Global service instance
schema_org_service = SchemaOrgService()
