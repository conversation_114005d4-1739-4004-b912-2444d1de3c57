{"export_info": {"export_format": "JSON", "export_date": "2025-08-28T09:33:29.955044", "generated_by": " Metadata Harvesting System", "version": "2.0"}, "basic_info": {"id": "6878ef9df8e68ee585d7c854", "title": "2025 7 7 Iolp Buildings", "source": "User upload: 20250717_134205_2025-7-7-iolp-buildings.xlsx", "category": "Technology", "data_type": "Spreadsheet", "format": "xlsx", "file_size": null, "license": "Open Data Commons Open Database License (ODbL)", "created_at": "2025-07-17T12:42:05.252000", "updated_at": "2025-07-17T12:44:47.019000", "source_url": "", "persistent_identifier": null, "encoding_format": null, "status": "completed"}, "description": {"main_description": "Dataset Description: Title: 2025 7 7 Iolp Buildings This dataset contains information about 8,645 buildings, providing details such as location, ownership, address, and geographical coordinates. The data is organized into 18 fields, which are described below: 1. Location Code (object): A unique identifier for each building's location. 2. Real Property Asset Name (object): The name of the building or property. 3. Installation Name (object): The name of the installation or organization to which the building belongs. 4. Owned or Leased (object): A categorical variable indicating whether the building is owned or leased. 5. GSA Region (int64): An integer representing the General Services Administration (GSA) region in which the building is located. 6. Street Address (object): The street address of the building. 7. City (object): The city where the building is located. 8. State (object): The state where the building is located. 9. Zip Code (int64): The zip code of the building's location. 10. Latitude (float64): The geographical coordinate indicating the building's north-south position. Potential research applications and use cases for this dataset include: Real estate analysis: Investigating trends in building ownership, location, and asset values. Urban planning: Examining the distribution of buildings and their impact on city infrastructure and resources. Geographic information systems (GIS) analysis: Integrating this dataset with other geospatial data to explore relationships between building locations and environmental, social, or economic factors. Facility management: Utilizing the data to optimize building maintenance, allocation, and utilization. Key insights about the data characteristics include: The dataset provides a comprehensive view of building information, including ownership, location, and geographical coordinates. The data is organized into distinct fields, allowing for easy filtering, sorting, and analysis. The inclusion of GSA region information enables comparisons and aggregations across different regions. Suitable analysis methods or techniques for this dataset include: Descriptive statistics: Computing measures such as mean, median, and standard deviation to summarize the data. Data visualization: Creating charts, graphs, and maps to explore patterns and trends in the data. Spatial analysis: Applying GIS techniques to analyze the geographical distribution and clustering of buildings. Machine learning: Using algorithms to predict building characteristics based on other variables, such as location, ownership, or GSA region.", "structured_description": null, "auto_generated_description": null}, "data_statistics": {"record_count": 8645, "field_count": 18, "field_names": ["Location Code", " Real Property Asset Name", " Installation Name", " Owned or Leased", " GSA Region", " Street Address", " City", " State", " Zip Code", " Latitude", " Longitude", " Building Rentable Square Feet", " Available Square Feet", " Construction Date", " Congressional District", " Congressional District Representative Name", " Building Status", " Real Property Asset Type"], "data_types": ["object", " int64", " float64"], "data_distribution_types": null}, "metadata_fields": {"tags": ["asset", " building", " column", " contains", " data", " dated", " descriptive", " field", " general", " geographic", " labeled", " location", " metrics", " name", " numerical", " organizational", " personal", " property", " qualitative", " quantitative"], "keywords": ["[\"contains\"", " \"building\"", " \"name\"", " \"data\"", " \"real\"", " \"property\"", " \"asset\"", " \"type\"", " \"field\"", " \"column\"", " \"active\"", " \"code\"", " \"congressional\"", " \"district\"", " \"information\"", " \"2025\"", " \"<PERSON><PERSON><PERSON>\"", " \"Buildings\"", " \"Location Code\"", " \"Real Property Asset Name\"]"], "use_cases": ["\"data analysis", " research studies", " machine learning\""], "entities": null, "sentiment": null}, "quality_assessment": {"overall_quality_score": 56.907007575757575, "completeness": 60.30303030303029, "consistency": 12.0, "accuracy": 79.0, "timeliness": 75.5, "conformity": 52.54166666666667, "integrity": 81.0, "issues": ["Missing required field: description", "Inconsistent field definition for Location Code", "Inconsistent field definition for Real Property Asset Name", "Inconsistent field definition for Owned or Leased", "Inconsistent field definition for GSA Region", "Inconsistent field definition for Street Address", "Inconsistent field definition for City", "Inconsistent field definition for State", "Inconsistent field definition for Zip Code", "Inconsistent field definition for Latitude", "Inconsistent field definition for Longitude", "Inconsistent field definition for Building Rentable Square Feet", "Inconsistent field definition for Construction Date", "Inconsistent field definition for Congressional District", "Inconsistent field definition for Congressional District Representative Name", "Inconsistent field definition for Building Status", "Inconsistent field definition for Real Property Asset Type", "Schema.org metadata not defined"], "recommendations": ["Add description to improve dataset completeness", "Consider adding these fields to enhance completeness: publisher, tags, sample_data", "Specify update frequency to improve timeliness assessment", "Define Schema.org metadata to improve standards conformity", "Consider adding a persistent identifier (<PERSON>O<PERSON>, Handle) to improve findability", "Add detailed description (>50 characters) to improve findability", "Specify a clear license to improve reusability"], "assessment_date": "2025-07-17T12:44:18.578000"}, "fair_compliance": {"overall_score": 66.0, "is_compliant": false, "findable_score": 40.0, "accessible_score": 100.0, "interoperable_score": 44.0, "reusable_score": 80.0}, "standards_compliance": {"schema_org_compliant": false, "dublin_core": {}, "dcat_metadata": {}, "json_ld": {}}, "visualizations": {"quality_metrics": {"type": "quality_metrics", "data": {"overall_score": 56.907007575757575, "completeness": 60.30303030303029, "consistency": 12.0, "accuracy": 79.0, "fair_compliant": false}, "chart_config": {"type": "radar", "title": "Data Quality Assessment", "description": "Comprehensive quality metrics for the dataset"}}, "data_overview": {"type": "data_overview", "data": {"record_count": 8645, "field_count": 18, "data_size": "8645 rows × 18 columns", "estimated_size_mb": 1.19}, "chart_config": {"type": "info_cards", "title": "Dataset Overview", "description": "Basic statistics about the dataset structure"}}, "field_analysis": {"type": "field_analysis", "data": {"type_distribution": {"object": 9, "int64": 2, "float64": 5}, "numeric_fields": 0, "text_fields": 0, "categorical_fields": 0, "datetime_fields": 0}, "chart_config": {"type": "pie", "title": "Field Type Distribution", "description": "Distribution of data types across dataset fields"}}, "keyword_cloud": {"type": "keyword_cloud", "data": {"keywords": [{"text": "contains", "weight": 21}, {"text": "building", "weight": 20}, {"text": "name", "weight": 19}, {"text": "data", "weight": 18}, {"text": "real", "weight": 17}, {"text": "property", "weight": 16}, {"text": "asset", "weight": 15}, {"text": "type", "weight": 14}, {"text": "field", "weight": 13}, {"text": "column", "weight": 12}, {"text": "active", "weight": 11}, {"text": "code", "weight": 10}, {"text": "congressional", "weight": 9}, {"text": "district", "weight": 8}, {"text": "information", "weight": 7}], "total_keywords": 15}, "chart_config": {"type": "wordcloud", "title": "Content Keywords", "description": "Most relevant keywords extracted from dataset content"}}, "metadata_completeness": {"type": "metadata_completeness", "data": {"completeness_fields": {"title": true, "description": true, "source": true, "category": true, "tags": true, "quality_assessed": true, "schema_defined": false}, "completed_count": 6, "total_count": 7, "completeness_percentage": 85.7}, "chart_config": {"type": "progress_bar", "title": "Metadata Completeness", "description": "85.7% of metadata fields are complete"}}, "data_distribution": {"type": "histogram", "data": {"bins": ["-112.3-12699.8", "12699.8-25511.8", "25511.8-38323.8", "38323.8-51135.9", "51135.9-63947.9", "63947.9-76759.9", "76759.9-89572.0", "89572.0-102384.0", "102384.0-115196.1", "115196.1-128008.1"], "frequencies": [55, 4, 3, 1, 2, 0, 4, 0, 0, 1], "xlabel": "Value Range", "total_values": 70}, "chart_config": {"type": "histogram", "title": "Data Distribution", "description": "Distribution of 70 numeric values"}}, "correlation_analysis": {"type": "heatmap", "data": {"matrix": [[1.0, 0.4391022341013675, 0.20472283466063854, -0.4667305743841781, -0.1125488364680494], [0.4391022341013675, 1.0, 0.2531881134760431, -0.9876578263874282, -0.7143321116539472], [0.2047228346606385, 0.2531881134760431, 1.0, -0.1640565378542022, -0.0034433057487224055], [-0.4667305743841781, -0.9876578263874283, -0.16405653785420224, 1.0, 0.6846624298817953], [-0.1125488364680494, -0.714332111653947, -0.0034433057487224055, 0.6846624298817952, 1.0]], "labels": {"x": ["GSA Region", "Zip Code", "Latitude", "Longitude", "Building Rentable Square Feet"], "y": ["GSA Region", "Zip Code", "Latitude", "Longitude", "Building Rentable Square Feet"]}, "max": 1.0}, "chart_config": {"type": "heatmap", "title": "Correlation Analysis", "description": "Correlation matrix for 5 numeric fields"}}, "trend_analysis": {"type": "line", "data": {"labels": ["Record 0", "Record 10", "Record 20", "Record 30", "Record 40", "Record 50", "Record 60", "Record 70", "Record 80", "Record 90"], "values": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9], "x": [0, 10, 20, 30, 40, 50, 60, 70, 80, 90], "y": [87.0, 83.1, 87.2, 83.3, 87.4, 83.5, 87.6, 83.7, 87.8, 83.9]}, "chart_config": {"type": "line", "title": "Data Quality Trend", "description": "Quality trend across 10 sample points"}}, "generated_at": "2025-07-17T13:44:46.947748", "visualization_version": "3.0"}, "health_report": null, "ai_compliance": null, "processing_metadata": null, "python_examples": {"basic_loading": "# Load 2025 7 7 Iolp Buildings dataset\nimport pandas as pd\nimport numpy as np\n\n# Load the dataset\ndf = pd.read_csv('2025_7_7_iolp_buildings.csv')\n\n# Basic information\nprint(f\"Dataset shape: {df.shape}\")\nprint(f\"Columns: {list(df.columns)}\")\nprint(df.head())", "data_exploration": "# Data exploration for 2025 7 7 Iolp Buildings\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Dataset overview\nprint(df.info())\nprint(df.describe())\n\n# Check for missing values\nprint(df.isnull().sum())\n\n# Basic visualizations\nplt.figure(figsize=(10, 6))\n# Analyze Real Property Asset Name\n# Analyze Installation Name"}, "nlp_analysis": {"entities": null, "sentiment": null, "summary": null, "language_detected": "en", "content_analysis": null}, "technical_metadata": {"file_format": "xlsx", "encoding": null, "delimiter": null, "compression": null, "checksum": null, "file_path": "C:\\Users\\<USER>\\Desktop\\AIMetaHarvest\\app\\uploads\\20250717_134205_2025-7-7-iolp-buildings.xlsx", "upload_date": "2025-07-17T12:42:05.252000", "last_modified": "2025-07-17T12:44:47.019000"}, "data_quality_metrics": {"completeness_score": null, "consistency_score": null, "accuracy_score": null, "validity_score": null, "uniqueness_score": null, "missing_values_count": null, "duplicate_records_count": null}, "statistical_summary": null, "data_lineage": {"source_system": null, "collection_method": null, "processing_history": null, "transformation_applied": null, "validation_rules": null}}