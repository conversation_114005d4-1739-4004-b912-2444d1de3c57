{"export_info": {"export_format": "JSON", "export_date": "2025-09-04T10:09:03.265497", "generated_by": " Metadata Harvesting System", "version": "2.0"}, "basic_info": {"id": "68b95622c5af8e8be94abcf0", "title": "Test Dataset", "source": "User upload: 20250904_100434_test_dataset.csv", "category": "Technology", "data_type": "Tabular", "format": "csv", "file_size": null, "license": "Open Data Commons Open Database License (ODbL)", "created_at": "2025-09-04T09:04:34.875000", "updated_at": "2025-09-04T09:06:35.527000", "source_url": "", "persistent_identifier": null, "encoding_format": null, "status": "completed"}, "description": {"main_description": "Dataset Description: Test Dataset in the Education Domain 1. DATASET NATURE: The Test Dataset, encompassing 10 records across 4 fields, serves as a concise and manageable collection of educational data, designed to facilitate exploratory data analysis, methodological experimentation, or pedagogical demonstrations. Despite its diminutive scale, this dataset retains the fundamental structure and characteristics of larger educational datasets, thereby offering valuable insights into data analysis within the education domain. 2. DATA STRUCTURE: The dataset's organization comprises four key variables: name, age, city, and occupation. These variables represent common demographic and professional data points found in educational research, offering a foundation for examining relationships and patterns among these attributes. The \"name\" field, although anonymous, provides a unique identifier for each record, enabling data manipulation and tracking. The \"age\" variable, measured in whole years, allows for age-based cohort analysis or lifetime value estimations. The \"city\" field, denoting place of residence, offers opportunities for geographical clustering, regional comparisons, or urban-rural disparity studies. Lastly, the \"occupation\" field encapsulates the subjects' professional roles, paving the way for occupational category analysis, labor market explorations, or skills gap investigations. 3. RESEARCH VALUE: This compact dataset can address a multitude of research questions, such as: - What are the age distributions and occupational patterns among this sample? - Are there any geographical disparities in the dataset with respect to age or occupation? - Can we identify any correlations between age, city, and occupation within this dataset? 4. ANALYTICAL POTENTIAL: Statistical methods and machine learning applications abound in this dataset. Researchers can employ descriptive statistics to summarize and visualize the data, inferential statistics to test hypotheses, or correlation and regression analyses to uncover relationships among variables. Machine learning techniques, such as clustering algorithms, decision trees, or neural networks, can further reveal hidden patterns or predict outcomes based on the given features. Data visualization techniques, such as scatter plots, heatmaps, or choropleth maps, can enhance the understanding and communication of findings. 5. USE CASES: Practitioners, researchers, or analysts may utilize this dataset in various ways, including: - Educators seeking to illustrate data analysis techniques in a classroom setting - Researchers developing and testing new analytical methods or visualization tools - Policy analysts examining the implications of age, location, or occupation on educational outcomes 6. DATA CHARACTERISTICS: Temporal coverage for this dataset is not explicitly stated, given its anonymized and simulated nature. However, the data can be contextualized within a contemporary educational landscape, allowing for analyses pertinent to current issues and trends. Geographical scope is limited to the cities represented within the dataset, yet these can be generalized to broader urban or rural categories for comparative purposes. Measurement units are standardized, with age measured in whole years, and city and occupation represented as text fields. This standardization enables straightforward data manipulation and analysis, thereby maximizing the dataset's utility for researchers and practitioners alike.", "structured_description": null, "auto_generated_description": null}, "data_statistics": {"record_count": 10, "field_count": 4, "field_names": ["name", " age", " city", " occupation"], "data_types": ["object", " int64"], "data_distribution_types": null}, "metadata_fields": {"tags": ["age", " city", " column", " contains", " data", " dataset", " descriptive", " field", " general", " geographic", " information", " labeled", " metrics", " name", " numerical", " occupation", " personal", " qualitative", " quantitative", " textual"], "keywords": ["[\"contains\"", " \"name\"", " \"city\"", " \"occupation\"", " \"data\"", " \"dataset\"", " \"field\"", " \"column\"", " \"information\"", " \"age\"", " \"san\"", " \"type\"", " \"this\"", " \"csv\"", " \"john\"", " \"Test\"]"], "use_cases": ["\"data analysis", " research studies", " machine learning\""], "entities": null, "sentiment": null}, "quality_assessment": {"overall_quality_score": 66.73200757575758, "completeness": 60.30303030303029, "consistency": 60.0, "accuracy": 79.0, "timeliness": 75.5, "conformity": 54.041666666666664, "integrity": 81.0, "issues": ["Missing required field: description", "Inconsistent field definition for name", "Inconsistent field definition for age", "Inconsistent field definition for city", "Inconsistent field definition for occupation", "Schema.org metadata not defined"], "recommendations": ["Add description to improve dataset completeness", "Consider adding these fields to enhance completeness: tags, license, schema", "Specify update frequency to improve timeliness assessment", "Define Schema.org metadata to improve standards conformity", "Consider adding a persistent identifier (<PERSON>O<PERSON>, Handle) to improve findability", "Add detailed description (>50 characters) to improve findability", "Specify a clear license to improve reusability"], "assessment_date": "2025-09-04T09:06:10.524000"}, "fair_compliance": {"overall_score": 78.0, "is_compliant": true, "findable_score": 72.0, "accessible_score": 100.0, "interoperable_score": 60.0, "reusable_score": 80.0}, "standards_compliance": {"schema_org_compliant": false, "dublin_core": {}, "dcat_metadata": {}, "json_ld": {}}, "visualizations": {"quality_metrics": {"type": "quality_metrics", "data": {"overall_score": 66.73200757575758, "completeness": 60.30303030303029, "consistency": 60.0, "accuracy": 79.0, "fair_compliant": false}, "chart_config": {"type": "radar", "title": "Data Quality Assessment", "description": "Comprehensive quality metrics for the dataset"}}, "data_overview": {"type": "data_overview", "data": {"record_count": 10, "field_count": 4, "data_size": "10 rows × 4 columns", "estimated_size_mb": 0.0}, "chart_config": {"type": "info_cards", "title": "Dataset Overview", "description": "Basic statistics about the dataset structure"}}, "field_analysis": {"type": "field_analysis", "data": {"type_distribution": {"object": 3, "int64": 1}, "numeric_fields": 0, "text_fields": 0, "categorical_fields": 0, "datetime_fields": 0}, "chart_config": {"type": "pie", "title": "Field Type Distribution", "description": "Distribution of data types across dataset fields"}}, "keyword_cloud": {"type": "keyword_cloud", "data": {"keywords": [{"text": "contains", "weight": 21}, {"text": "name", "weight": 20}, {"text": "city", "weight": 19}, {"text": "occupation", "weight": 18}, {"text": "data", "weight": 17}, {"text": "dataset", "weight": 16}, {"text": "field", "weight": 15}, {"text": "column", "weight": 14}, {"text": "information", "weight": 13}, {"text": "age", "weight": 12}, {"text": "san", "weight": 11}, {"text": "type", "weight": 10}, {"text": "this", "weight": 9}, {"text": "csv", "weight": 8}, {"text": "john", "weight": 7}], "total_keywords": 15}, "chart_config": {"type": "wordcloud", "title": "Content Keywords", "description": "Most relevant keywords extracted from dataset content"}}, "metadata_completeness": {"type": "metadata_completeness", "data": {"completeness_fields": {"title": true, "description": true, "source": true, "category": true, "tags": true, "quality_assessed": true, "schema_defined": false}, "completed_count": 6, "total_count": 7, "completeness_percentage": 85.7}, "chart_config": {"type": "progress_bar", "title": "Metadata Completeness", "description": "85.7% of metadata fields are complete"}}, "data_distribution": {"type": "histogram", "data": {"bins": ["25.0-26.0", "26.0-27.0", "27.0-28.0", "28.0-29.0", "29.0-30.0", "30.0-31.0", "31.0-32.0", "32.0-33.0", "33.0-34.0", "34.0-35.0"], "frequencies": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "xlabel": "Value Range", "total_values": 10}, "chart_config": {"type": "histogram", "title": "Data Distribution", "description": "Distribution of 10 numeric values"}}, "correlation_analysis": {"type": "heatmap", "data": {"matrix": [], "labels": {}}, "chart_config": {"type": "heatmap", "title": "Correlation Analysis", "description": "Insufficient numeric data for correlation"}}, "trend_analysis": {"type": "line", "data": {"labels": ["Record 0"], "values": [87.0], "x": [0], "y": [87.0]}, "chart_config": {"type": "line", "title": "Data Quality Trend", "description": "Quality trend across 1 sample points"}}, "generated_at": "2025-09-04T10:06:35.509740", "visualization_version": "3.0"}, "health_report": null, "ai_compliance": null, "processing_metadata": null, "python_examples": {"basic_loading": "# Load Test Dataset dataset\nimport pandas as pd\nimport numpy as np\n\n# Load the dataset\ndf = pd.read_csv('test_dataset.csv')\n\n# Basic information\nprint(f\"Dataset shape: {df.shape}\")\nprint(f\"Columns: {list(df.columns)}\")\nprint(df.head())", "data_exploration": "# Data exploration for Test Dataset\nimport matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Dataset overview\nprint(df.info())\nprint(df.describe())\n\n# Check for missing values\nprint(df.isnull().sum())\n\n# Basic visualizations\nplt.figure(figsize=(10, 6))\n# Analyze age\n# Analyze city"}, "nlp_analysis": {"entities": null, "sentiment": null, "summary": null, "language_detected": "en", "content_analysis": null}, "technical_metadata": {"file_format": "csv", "encoding": null, "delimiter": null, "compression": null, "checksum": null, "file_path": "C:\\Users\\<USER>\\Desktop\\MetaGenF\\app\\uploads\\20250904_100434_test_dataset.csv", "upload_date": "2025-09-04T09:04:34.875000", "last_modified": "2025-09-04T09:06:35.527000"}, "data_quality_metrics": {"completeness_score": null, "consistency_score": null, "accuracy_score": null, "validity_score": null, "uniqueness_score": null, "missing_values_count": null, "duplicate_records_count": null}, "statistical_summary": null, "data_lineage": {"source_system": null, "collection_method": null, "processing_history": null, "transformation_applied": null, "validation_rules": null}}